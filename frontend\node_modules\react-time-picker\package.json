{"name": "react-time-picker", "version": "6.6.0", "description": "A time picker for your React app.", "type": "module", "sideEffects": ["*.css"], "main": "./dist/cjs/index.js", "module": "./dist/esm/index.js", "source": "./src/index.ts", "types": "./dist/cjs/index.d.ts", "exports": {".": {"import": "./dist/esm/index.js", "require": "./dist/cjs/index.js"}, "./dist/TimeInput": {"import": "./dist/esm/TimeInput.js", "require": "./dist/cjs/TimeInput.js"}, "./dist/TimeInput.js": {"import": "./dist/esm/TimeInput.js", "require": "./dist/cjs/TimeInput.js"}, "./dist/TimeInput/Hour12Input": {"import": "./dist/esm/TimeInput/Hour12Input.js", "require": "./dist/cjs/TimeInput/Hour12Input.js"}, "./dist/TimeInput/Hour12Input.js": {"import": "./dist/esm/TimeInput/Hour12Input.js", "require": "./dist/cjs/TimeInput/Hour12Input.js"}, "./dist/TimeInput/Hour24Input": {"import": "./dist/esm/TimeInput/Hour24Input.js", "require": "./dist/cjs/TimeInput/Hour24Input.js"}, "./dist/TimeInput/Hour24Input.js": {"import": "./dist/esm/TimeInput/Hour24Input.js", "require": "./dist/cjs/TimeInput/Hour24Input.js"}, "./dist/TimeInput/MinuteInput": {"import": "./dist/esm/TimeInput/MinuteInput.js", "require": "./dist/cjs/TimeInput/MinuteInput.js"}, "./dist/TimeInput/MinuteInput.js": {"import": "./dist/esm/TimeInput/MinuteInput.js", "require": "./dist/cjs/TimeInput/MinuteInput.js"}, "./dist/TimeInput/SecondInput": {"import": "./dist/esm/TimeInput/SecondInput.js", "require": "./dist/cjs/TimeInput/SecondInput.js"}, "./dist/TimeInput/SecondInput.js": {"import": "./dist/esm/TimeInput/SecondInput.js", "require": "./dist/cjs/TimeInput/SecondInput.js"}, "./dist/TimeInput/AmPm": {"import": "./dist/esm/TimeInput/AmPm.js", "require": "./dist/cjs/TimeInput/AmPm.js"}, "./dist/TimeInput/AmPm.js": {"import": "./dist/esm/TimeInput/AmPm.js", "require": "./dist/cjs/TimeInput/AmPm.js"}, "./dist/cjs/TimeInput": "./dist/cjs/TimeInput.js", "./dist/cjs/TimeInput.js": "./dist/cjs/TimeInput.js", "./dist/cjs/TimeInput/Hour12Input": "./dist/cjs/TimeInput/Hour12Input.js", "./dist/cjs/TimeInput/Hour12Input.js": "./dist/cjs/TimeInput/Hour12Input.js", "./dist/cjs/TimeInput/Hour24Input": "./dist/cjs/TimeInput/Hour24Input.js", "./dist/cjs/TimeInput/Hour24Input.js": "./dist/cjs/TimeInput/Hour24Input.js", "./dist/cjs/TimeInput/MinuteInput": "./dist/cjs/TimeInput/MinuteInput.js", "./dist/cjs/TimeInput/MinuteInput.js": "./dist/cjs/TimeInput/MinuteInput.js", "./dist/cjs/TimeInput/SecondInput": "./dist/cjs/TimeInput/SecondInput.js", "./dist/cjs/TimeInput/SecondInput.js": "./dist/cjs/TimeInput/SecondInput.js", "./dist/cjs/TimeInput/AmPm": "./dist/cjs/TimeInput/AmPm.js", "./dist/cjs/TimeInput/AmPm.js": "./dist/cjs/TimeInput/AmPm.js", "./dist/esm/TimeInput": "./dist/esm/TimeInput.js", "./dist/esm/TimeInput.js": "./dist/esm/TimeInput.js", "./dist/esm/TimeInput/Hour12Input": "./dist/esm/TimeInput/Hour12Input.js", "./dist/esm/TimeInput/Hour12Input.js": "./dist/esm/TimeInput/Hour12Input.js", "./dist/esm/TimeInput/Hour24Input": "./dist/esm/TimeInput/Hour24Input.js", "./dist/esm/TimeInput/Hour24Input.js": "./dist/esm/TimeInput/Hour24Input.js", "./dist/esm/TimeInput/MinuteInput": "./dist/esm/TimeInput/MinuteInput.js", "./dist/esm/TimeInput/MinuteInput.js": "./dist/esm/TimeInput/MinuteInput.js", "./dist/esm/TimeInput/SecondInput": "./dist/esm/TimeInput/SecondInput.js", "./dist/esm/TimeInput/SecondInput.js": "./dist/esm/TimeInput/SecondInput.js", "./dist/esm/TimeInput/AmPm": "./dist/esm/TimeInput/AmPm.js", "./dist/esm/TimeInput/AmPm.js": "./dist/esm/TimeInput/AmPm.js", "./dist/TimePicker.css": "./dist/TimePicker.css"}, "scripts": {"build": "yarn build-js && yarn copy-styles", "build-js": "yarn build-js-esm && yarn build-js-cjs && yarn build-js-cjs-package", "build-js-esm": "tsc --project tsconfig.build.json --outDir dist/esm", "build-js-cjs": "tsc --project tsconfig.build.json --outDir dist/cjs --module commonjs --verbatimModuleSyntax false", "build-js-cjs-package": "echo '{\n  \"type\": \"commonjs\"\n}' > dist/cjs/package.json", "clean": "<PERSON><PERSON><PERSON> dist", "copy-styles": "cpy 'src/**/*.css' dist", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "prepack": "yarn clean && yarn build", "prettier": "prettier --check . --cache", "test": "yarn lint && yarn tsc && yarn prettier && yarn unit", "tsc": "tsc", "unit": "vitest", "watch": "yarn build-js-esm --watch & yarn build-js-cjs --watch & nodemon --watch src --ext css --exec \"yarn copy-styles\""}, "keywords": ["react", "time", "time-picker"], "author": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "dependencies": {"@wojtekmaj/date-utils": "^1.1.3", "clsx": "^2.0.0", "get-user-locale": "^2.2.1", "make-event-props": "^1.6.0", "prop-types": "^15.6.0", "react-clock": "^4.5.0", "react-fit": "^1.7.0", "update-input-width": "^1.4.0"}, "devDependencies": {"@testing-library/dom": "^9.0.0", "@testing-library/jest-dom": "^6.0.0", "@testing-library/react": "^14.0.0", "@testing-library/user-event": "^14.4.0", "@types/node": "*", "@types/react": "*", "cpy-cli": "^5.0.0", "eslint": "^8.26.0", "eslint-config-wojtekmaj": "^0.9.0", "happy-dom": "^12.6.0", "nodemon": "^3.0.0", "prettier": "^3.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "rimraf": "^3.0.0", "typescript": "^5.3.2", "vitest": "^1.0.2", "vitest-canvas-mock": "^0.2.2"}, "peerDependencies": {"@types/react": "^16.8.0 || ^17.0.0 || ^18.0.0", "react": "^16.8.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.8.0 || ^17.0.0 || ^18.0.0"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}, "publishConfig": {"access": "public", "provenance": true}, "files": ["dist", "src"], "repository": {"type": "git", "url": "https://github.com/wojtekmaj/react-time-picker.git", "directory": "packages/react-time-picker"}, "funding": "https://github.com/wojtekmaj/react-time-picker?sponsor=1"}