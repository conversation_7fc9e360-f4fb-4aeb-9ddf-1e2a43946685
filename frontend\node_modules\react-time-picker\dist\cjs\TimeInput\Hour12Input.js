"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __rest = (this && this.__rest) || function (s, e) {
    var t = {};
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                t[p[i]] = s[p[i]];
        }
    return t;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
var react_1 = __importDefault(require("react"));
var date_utils_1 = require("@wojtekmaj/date-utils");
var Input_js_1 = __importDefault(require("./Input.js"));
var dates_js_1 = require("../shared/dates.js");
var utils_js_1 = require("../shared/utils.js");
function Hour12Input(_a) {
    var amPm = _a.amPm, maxTime = _a.maxTime, minTime = _a.minTime, value = _a.value, otherProps = __rest(_a, ["amPm", "maxTime", "minTime", "value"]);
    var maxHour = (0, utils_js_1.safeMin)(12, maxTime &&
        (function () {
            var _a = (0, dates_js_1.convert24to12)((0, date_utils_1.getHours)(maxTime)), maxHourResult = _a[0], maxAmPm = _a[1];
            if (maxAmPm !== amPm) {
                // pm is always after am, so we should ignore validation
                return null;
            }
            return maxHourResult;
        })());
    var minHour = (0, utils_js_1.safeMax)(1, minTime &&
        (function () {
            var _a = (0, dates_js_1.convert24to12)((0, date_utils_1.getHours)(minTime)), minHourResult = _a[0], minAmPm = _a[1];
            if (
            // pm is always after am, so we should ignore validation
            minAmPm !== amPm ||
                // If minHour is 12 am/pm, user should be able to enter 12, 1, ..., 11.
                minHourResult === 12) {
                return null;
            }
            return minHourResult;
        })());
    var value12 = value ? (0, dates_js_1.convert24to12)(value)[0].toString() : '';
    return (react_1.default.createElement(Input_js_1.default, __assign({ max: maxHour, min: minHour, name: "hour12", nameForClass: "hour", value: value12 }, otherProps)));
}
exports.default = Hour12Input;
