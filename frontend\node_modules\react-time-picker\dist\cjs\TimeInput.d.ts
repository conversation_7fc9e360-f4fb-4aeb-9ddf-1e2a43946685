/// <reference types="react" />
import type { Detail, LooseValuePiece, Value } from './shared/types.js';
type TimeInputProps = {
    amPmAriaLabel?: string;
    autoFocus?: boolean;
    className: string;
    disabled?: boolean;
    format?: string;
    hourAriaLabel?: string;
    hourPlaceholder?: string;
    isClockOpen?: boolean | null;
    locale?: string;
    maxDetail?: Detail;
    maxTime?: string;
    minTime?: string;
    minuteAriaLabel?: string;
    minutePlaceholder?: string;
    name?: string;
    nativeInputAriaLabel?: string;
    onChange?: (value: Value, shouldCloseClock: boolean) => void;
    onInvalidChange?: () => void;
    required?: boolean;
    secondAriaLabel?: string;
    secondPlaceholder?: string;
    value?: LooseValuePiece;
};
export default function TimeInput({ amPmAriaLabel, autoFocus, className, disabled, format, hourAriaLabel, hourPlaceholder, isClockOpen: isClockOpenProps, locale, maxDetail, maxTime, minTime, minuteAriaLabel, minutePlaceholder, name, nativeInputAriaLabel, onChange: onChangeProps, onInvalidChange, required, secondAriaLabel, secondPlaceholder, value: valueProps, }: TimeInputProps): JSX.Element;
export {};
